import type { Metada<PERSON> } from "next"
import { Montser<PERSON> } from "next/font/google"
import "./globals.css"
import { AdminAuthProvider } from "@/contexts/AdminAuthContext"
import { ErrorBoundary } from "@/components/ErrorBoundary"

const montserrat = Montserrat({
  variable: "--font-montserrat",
  subsets: ["latin"],
  display: "swap",
})

export const metadata: Metadata = {
  title: "Adukrom Kingdom - Admin Dashboard",
  description: "Administrative dashboard for Adukrom Kingdom management",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className={montserrat.variable}>
      <body>
        <ErrorBoundary>
          <AdminAuthProvider>
            {children}
          </AdminAuthProvider>
        </ErrorBoundary>
      </body>
    </html>
  )
}
