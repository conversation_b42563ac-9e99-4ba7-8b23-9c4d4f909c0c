'use client';


import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { Eye } from 'lucide-react';
import { ActionButtons } from '@/components/shared/actions/action-buttons';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';


import { News } from '@/types/news';

interface NewsTableProps {
  data: News[];
  onUpdate: (id: string, updates: Partial<Omit<News, 'id' | 'createdAt' | 'updatedAt' | 'publishedAt'>>) => Promise<boolean>;
  onDelete?: (id: string) => Promise<void>;
  onToggleFeatured: (id: string, currentStatus: boolean) => Promise<void>;
  updatingId?: string | null;
  deletingId?: string | null;
}

export function NewsTable({ 
  data, 
  onUpdate, 
  onDelete,
  onToggleFeatured,
  updatingId = null, 

}: NewsTableProps) {
  const router = useRouter();
  const { toast } = useToast();

  const handleStatusChange = async (id: string, newStatus: 'draft' | 'published') => {
    try {
      await onUpdate(id, { status: newStatus });
      toast({
        title: 'Success',
        description: `News status updated to ${newStatus}`,
      });
    } catch (error) {
      console.error('Error updating status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update status',
        variant: 'destructive',
      });
    }
  };

  const handleFeaturedChange = async (id: string, isFeatured: boolean) => {
    try {
      await onToggleFeatured(id, isFeatured);
      // No need to show success toast here as it's handled in the parent component
    } catch (error) {
      console.error('Error updating featured status:', error);
      // Error toast is handled in the parent component
    }
  };

  return (
    <div className="space-y-4">
      <div className="rounded-lg border border-gray-200 shadow-lg bg-white overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[300px]">News</TableHead>
              <TableHead className="text-center">Status</TableHead>
              <TableHead className="text-center">Featured</TableHead>
              <TableHead className="text-center">Images</TableHead>
              <TableHead className="text-right">Published</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.length > 0 ? (
              data.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-10 w-10 rounded-md">
                        <AvatarImage src={item.featuredImage} alt={item.title} />
                        <AvatarFallback className="rounded-md">
                          <Eye className="h-5 w-5 text-muted-foreground" />
                        </AvatarFallback>
                      </Avatar>
                      <div className="space-y-0.5">
                        <div className="font-medium line-clamp-1 text-gray-900">{item.title}</div>
                        <div className="text-xs text-gray-600 line-clamp-1">
                          {item.excerpt}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <Badge
                      variant={item.status === 'published' ? 'success' : 'outline'}
                      className="capitalize"
                    >
                      {item.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-center">
                    <Switch
                      checked={item.featured}
                      onCheckedChange={(checked) => handleFeaturedChange(item.id, checked)}
                      disabled={updatingId === item.id}
                      className={updatingId === item.id ? 'opacity-50' : ''}
                    />
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="flex items-center justify-center space-x-1">
                      <span className="text-sm font-medium text-gray-700">
                        {Array.isArray(item.images) ? item.images.length : 0}
                      </span>
                      <Eye className="h-4 w-4 text-gray-500" />
                    </div>
                  </TableCell>
                  <TableCell className="text-right text-sm text-gray-600">
                    {item.publishedAt
                      ? format(new Date(item.publishedAt), 'MMM d, yyyy')
                      : 'Not published'}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 p-0 text-green-600 hover:text-green-800 hover:bg-green-50"
                        onClick={() => router.push(`/dashboard/news/${item.id}`)}
                        title="View"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <ActionButtons
                        editHref={`/dashboard/news/${item.id}/edit`}
                        onDelete={async () => {
                          if (!onDelete) return false;
                          try {
                            await onDelete(item.id);
                            return true;
                          } catch (error) {
                            console.error('Error deleting news article:', error);
                            return false;
                          }
                        }}
                        deleteTitle={`Delete ${item.title}`}
                        deleteDescription="Are you sure you want to delete this news article? This action cannot be undone."
                        onSuccess={() => {}}
                        editButtonProps={{
                          variant: 'ghost',
                          size: 'icon',
                          className: 'h-8 w-8 text-royal-blue-600 hover:text-royal-blue-800 hover:bg-royal-blue-50',
                          disabled: updatingId === item.id
                        }}
                        deleteButtonProps={{
                          variant: 'ghost',
                          size: 'icon',
                          className: 'h-8 w-8 text-red-600 hover:text-red-800 hover:bg-red-50',
                          disabled: updatingId === item.id
                        }}
                      />
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center text-gray-600">
                  No news articles found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
