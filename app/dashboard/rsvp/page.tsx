'use client';

import { useState, useEffect, useCallback } from 'react';
import { collection, getDocs, query, orderBy, writeBatch, doc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Button } from '@/components/ui/button';
import { Upload, AlertCircle } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { RSVP, CSVRow } from '@/types/rsvp';
import { RSVPsTable } from './components/RSVPsTable';

export default function RSVPPage() {
  const [rsvp, setRsvp] = useState<RSVP[]>([]);
  const [loading, setLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch RSVPs from Firestore
  const fetchRSVPs = useCallback(async () => {
    try {
      setLoading(true);
      const q = query(collection(db, 'rsvp'), orderBy('submittedAt', 'desc'));
      const querySnapshot = await getDocs(q);
      
      const rsvpData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as RSVP[];
      
      setRsvp(rsvpData);
      setError(null);
    } catch (err) {
      console.error('Error fetching RSVPs:', err);
      setError('Failed to load RSVPs. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchRSVPs();
  }, [fetchRSVPs]);

  // Export RSVPs to CSV
  const exportToCSV = useCallback((filteredRSVP: RSVP[]) => {
    setIsExporting(true);
    try {
      const csvRows: CSVRow[] = filteredRSVP.map(rsvp => ({
        'First Name': rsvp.firstName || '',
        'Last Name': rsvp.lastName || '',
        'Email': rsvp.email || '',
        'Phone': rsvp.phone || '',
        'Country': rsvp.country || '',
        'Attendance Type': rsvp.attendanceType || '',
        'Events': Array.isArray(rsvp.events) ? rsvp.events.join(', ') : '',
        'Reminder Preference': Array.isArray(rsvp.reminderPreference) 
          ? rsvp.reminderPreference.join(', ')
          : '',
        'Notes': rsvp.notes || '',
        'Submitted At': rsvp.submittedAt 
          ? new Date(rsvp.submittedAt).toLocaleString() 
          : 'Unknown'
      }));

      const headers = Object.keys(csvRows[0] || {});
      const csvContent = [
        headers.join(','),
        ...csvRows.map(row => 
          headers.map(header => 
            `"${String(row[header as keyof CSVRow] || '').replace(/"/g, '""')}"` // Update CSV content
          ).join(',')
        )
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `rsvps-${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: 'Export successful',
        description: `${filteredRSVP.length} RSVPs exported to CSV.`,
      });
    } catch (err) {
      console.error('Error exporting to CSV:', err);
      toast({
        title: 'Export failed',
        description: 'Failed to export RSVPs to CSV.',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  }, []);

  // Import RSVPs from CSV
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsImporting(true);
    setError(null);

    try {
      const text = await file.text();
      const lines = text.split('\n');
      const headers = lines[0].split(',').map(h => h.replace(/^"|"$/g, '').trim());
      
      const batch = writeBatch(db);
      const rsvpsCollection = collection(db, 'rsvps');
      
      // Skip header row and process each line
      for (let i = 1; i < lines.length; i++) {
        if (!lines[i].trim()) continue;
        
        const values = lines[i].split(',').map(v => v.replace(/^"|"$/g, '').trim());
        const rsvpData: Partial<RSVP> = {};
        
        headers.forEach((header, index) => {
          const value = values[index] || '';
          switch (header) {
            case 'Events':
              rsvpData.events = value.split(',').map(e => e.trim()).filter(Boolean);
              break;
            case 'Reminder Preference':
              rsvpData.reminderPreference = value.split(',').map(r => r.trim().toLowerCase()).filter(Boolean) as ('sms' | 'email')[];
              break;
            case 'Submitted At':
              rsvpData.submittedAt = value || new Date().toISOString();
              break;
            default:
              const key = header.toLowerCase().replace(/\s+/g, '');
              if (key in rsvpData) {
                // @ts-ignore - Dynamic key assignment
                rsvpData[key] = value;
              }
          }
        });
        
        // Add a new document with auto-generated ID
        const newDocRef = doc(rsvpsCollection);
        batch.set(newDocRef, {
          ...rsvpData,
          _createdAt: new Date().toISOString(),
          _updatedAt: new Date().toISOString(),
          _type: 'rsvp'
        });
      }
      
      await batch.commit();
      await fetchRSVPs();
      
      toast({
        title: 'Import successful',
        description: 'RSVPs have been imported successfully.',
      });
    } catch (err) {
      console.error('Error importing RSVPs:', err);
      setError('Failed to import RSVPs. Please check the file format and try again.');
      toast({
        title: 'Import failed',
        description: 'Failed to import RSVPs. Please check the file format.',
        variant: 'destructive',
      });
    } finally {
      setIsImporting(false);
      // Reset file input
      if (event.target) event.target.value = '';
    }
  };

  return (
    <div className="container mx-auto p-6">
      <div className="flex flex-col space-y-4">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight text-royal-blue-900">RSVP Management</h1>
          <p className="text-gray-600">
            View and manage event RSVPs
          </p>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="flex justify-end">
          <Button asChild variant="outline" disabled={isImporting} className="border-royal-blue-600 text-royal-blue-600 hover:bg-royal-blue-50">
            <label className="cursor-pointer">
              <Upload className="mr-2 h-4 w-4" />
              Import CSV
              <input
                type="file"
                className="hidden"
                accept=".csv"
                onChange={handleFileUpload}
                disabled={isImporting}
              />
            </label>
          </Button>
        </div>

        <RSVPsTable
          rsvp={rsvp}
          loading={loading}
          onExport={exportToCSV}
          isExporting={isExporting}
        />
      </div>
    </div>
  );
}