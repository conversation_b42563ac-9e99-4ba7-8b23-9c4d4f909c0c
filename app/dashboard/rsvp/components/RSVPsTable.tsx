'use client';

import { useState, useMemo } from 'react';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Download, Search, Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { RSVP } from '@/types/rsvp';

interface RSVPsTableProps {
  rsvp: RSVP[];
  loading: boolean;
  onExport?: (filteredRSVP: RSVP[]) => void;
  isExporting: boolean;
  searchTerm?: string;
  onSearchChange?: (term: string) => void;
}

export function RSVPsTable({
  rsvp,
  loading,
  onExport,
  isExporting,
  searchTerm: initialSearchTerm = '',
  onSearchChange,
}: RSVPsTableProps) {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  
  const filteredRSVP = useMemo(() => {
    if (!searchTerm) return rsvp;
    return rsvp.filter(
      (rsvp) =>
        rsvp.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        rsvp.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        rsvp.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        rsvp.phone?.toLowerCase().includes(searchTerm)
    );
  }, [rsvp, searchTerm]);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    onSearchChange?.(term);
  };

  const handleExport = () => {
    onExport?.(filteredRSVP);
  };

  if (loading && rsvp.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-royal-blue-600" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div className="relative w-full md:max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="search"
            placeholder="Search RSVPs..."
            className="w-full pl-8 bg-white border-gray-300 text-gray-900"
            value={searchTerm}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>

        <Button
          variant="outline"
          onClick={handleExport}
          disabled={isExporting || filteredRSVP.length === 0}
          className="border-royal-blue-600 text-royal-blue-600 hover:bg-royal-blue-50"
        >
          {isExporting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Exporting...
            </>
          ) : (
            <>
              <Download className="mr-2 h-4 w-4" />
              Export CSV
            </>
          )}
        </Button>
      </div>

      <div className="rounded-lg border border-gray-200 shadow-lg bg-white overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Phone</TableHead>
              <TableHead>Events</TableHead>
              <TableHead>Attendance</TableHead>
              <TableHead>Submitted</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredRSVP.length > 0 ? (
              filteredRSVP.map((rsvp) => (
                <TableRow key={rsvp.id}>
                  <TableCell className="font-medium text-gray-900">
                    {rsvp.firstName} {rsvp.lastName}
                  </TableCell>
                  <TableCell className="text-gray-700">{rsvp.email}</TableCell>
                  <TableCell className="text-gray-700">{rsvp.phone}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {Array.isArray(rsvp.events) ? (
                        rsvp.events.map((event, i) => (
                          <span
                            key={i}
                            className="inline-flex items-center rounded-full bg-royal-blue-100 px-2 py-1 text-xs font-medium text-royal-blue-800"
                          >
                            {event}
                          </span>
                        ))
                      ) : null}
                    </div>
                  </TableCell>
                  <TableCell>
                    <span
                      className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                        rsvp.attendanceType === 'physical'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}
                    >
                      {rsvp.attendanceType || 'N/A'}
                    </span>
                  </TableCell>
                  <TableCell className="text-gray-700">
                    {rsvp.submittedAt
                      ? format(new Date(rsvp.submittedAt), 'MMM d, yyyy h:mm a')
                      : 'N/A'}
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center text-gray-600">
                  {searchTerm ? 'No matching RSVPs found.' : 'No RSVPs available.'}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
