'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { Calendar, Clock, MapPin, ExternalLink, Eye, EyeOff, Loader2 } from 'lucide-react';
import { ActionButtons } from '@/components/shared/actions/action-buttons';
import Link from 'next/link';

import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Event } from '@/types/events';
import { cn } from '@/lib/utils';

interface EventsTableProps {
  data: Event[];
  onDelete: (id: string) => void;
  onUpdate: (id: string, updates: Partial<Event>) => Promise<void>;
  loading?: boolean;
  updatingId?: string | null;
  deletingId?: string | null;
}

export function EventsTable({ 
  data, 
  onDelete, 
  onUpdate,
  loading = false,
  updatingId = null,
  deletingId = null 
}: EventsTableProps) {
  
  const handleStatusChange = async (id: string, newStatus: 'draft' | 'published') => {
    await onUpdate(id, { status: newStatus });
  };

  const handleFeaturedChange = async (id: string, isHighlighted: boolean) => {
    await onUpdate(id, { isHighlighted });
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (error) {
      return 'Invalid date';
    }
  };

  const formatTime = (dateString: string) => {
    try {
      return format(new Date(dateString), 'h:mm a');
    } catch (error) {
      return '';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-royal-blue-600" />
      </div>
    );
  }

  return (
    <div className="rounded-lg border border-gray-200 shadow-lg bg-white overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[300px]">Event</TableHead>
            <TableHead className="hidden md:table-cell">Date & Time</TableHead>
            <TableHead className="hidden lg:table-cell">Location</TableHead>
            <TableHead className="w-[120px]">Status</TableHead>
            <TableHead className="w-[100px]">Featured</TableHead>
            <TableHead className="w-[120px] text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} className="h-24 text-center text-gray-600">
                No events found.
              </TableCell>
            </TableRow>
          ) : (
            data.map((event) => (
              <TableRow key={event.id}>
                <TableCell className="font-medium">
                  <div className="flex items-center space-x-3">
                    {event.imageUrl ? (
                      <div className="relative h-12 w-12 flex-shrink-0 rounded-md overflow-hidden">
                        <img 
                          src={event.imageUrl} 
                          alt={event.imageAlt || event.title} 
                          className="h-full w-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="h-12 w-12 rounded-md bg-gray-100 flex items-center justify-center flex-shrink-0">
                        <Calendar className="h-5 w-5 text-gray-500" />
                      </div>
                    )}
                    <div className="min-w-0">
                      <p className="font-medium truncate text-gray-900">{event.title}</p>
                      <p className="text-sm text-gray-600 truncate">
                        {event.eventType}
                      </p>
                    </div>
                  </div>
                </TableCell>
                <TableCell className="hidden md:table-cell">
                  <div className="space-y-1">
                    <div className="flex items-center text-sm">
                      <Calendar className="mr-1 h-3.5 w-3.5 text-gray-500" />
                      <span className="text-gray-700">{formatDate(event.date)}</span>
                    </div>
                    {event.date && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Clock className="mr-1 h-3.5 w-3.5" />
                        <span>{formatTime(event.date)}</span>
                        {event.endDate && (
                          <>
                            <span className="mx-1">-</span>
                            <span>{formatTime(event.endDate)}</span>
                          </>
                        )}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell className="hidden lg:table-cell">
                  {event.location ? (
                    <div className="flex items-center text-sm">
                      <MapPin className="mr-1 h-3.5 w-3.5 text-gray-500 flex-shrink-0" />
                      <span className="truncate text-gray-700">{event.location}</span>
                    </div>
                  ) : (
                    <span className="text-gray-600 text-sm">TBD</span>
                  )}
                </TableCell>
                <TableCell>
                  <Badge
                    variant={event.status === 'published' ? 'success' : 'outline'}
                  >
                    {event.status === 'published' ? 'Published' : 'Draft'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <Switch
                      checked={event.isHighlighted}
                      onCheckedChange={(checked) => 
                        handleFeaturedChange(event.id, checked)
                      }
                      disabled={updatingId === event.id}
                      className={updatingId === event.id ? 'opacity-50' : ''}
                    />
                    <span className="ml-2 text-sm text-gray-600 hidden md:inline">
                      {event.isHighlighted ? 'Featured' : 'Regular'}
                    </span>
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end">
                    <ActionButtons
                      editHref={`/dashboard/events/${event.id}/edit`}
                      onDelete={async () => {
                        try {
                          await onDelete(event.id);
                          return true;
                        } catch (error) {
                          console.error('Error deleting event:', error);
                          return false;
                        }
                      }}
                      deleteTitle={`Delete ${event.title}`}
                      deleteDescription="Are you sure you want to delete this event? This action cannot be undone."
                      onSuccess={() => {}}
                      editButtonProps={{
                        variant: 'ghost',
                        size: 'icon',
                        className: 'h-8 w-8 text-royal-blue-600 hover:text-royal-blue-800 hover:bg-royal-blue-50',
                        disabled: updatingId === event.id
                      }}
                      deleteButtonProps={{
                        variant: 'ghost',
                        size: 'icon',
                        className: 'h-8 w-8 text-red-600 hover:text-red-800 hover:bg-red-50',
                        disabled: updatingId === event.id
                      }}
                    />
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
