'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { GalleryForm } from '../../_components/gallery-form';
import { GalleryItem } from '@/types/gallery';
import { useToast } from '@/hooks/use-toast';

export default function EditGalleryPage() {
  const { toast } = useToast();
  const router = useRouter();
  const params = useParams<{ id: string }>();
  const id = params?.id;
  
  if (!id) {
    throw new Error('Gallery item ID is required');
  }
  const [galleryItem, setGalleryItem] = useState<GalleryItem | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchGalleryItem = async () => {
      try {
        if (!id) return;
        
        const docRef = doc(db, 'gallery', id as string);
        const docSnap = await getDoc(docRef);
        
        if (docSnap.exists()) {
          const data = docSnap.data();
          // Handle Firestore timestamp conversion
          const convertTimestamp = (timestamp: any): Date => {
            if (!timestamp) return new Date();
            return typeof timestamp.toDate === 'function' ? timestamp.toDate() : new Date(timestamp);
          };
          
          // For backward compatibility, create images array from imageUrl if it exists
          const images = data.images || [];
          
          // If no images but imageUrl exists, create an image entry
          if ((!images || images.length === 0) && data.imageUrl) {
            images.push({
              url: data.imageUrl,
              storagePath: '', // This should be set if available
              name: 'image',
              size: 0,
              type: 'image/*'
            });
          }
          
          const item: GalleryItem = {
            id: docSnap.id,
            title: data.title || '',
            description: data.description || '',
            images: images,
            imageUrl: data.imageUrl || '', // Keep for backward compatibility
            storagePath: data.storagePath || '', // Required property
            thumbnailUrl: data.thumbnailUrl || data.imageUrl || '', // Keep for backward compatibility
            featured: data.featured || false, // Required property
            status: data.status || 'draft',
            createdAt: convertTimestamp(data.createdAt),
            updatedAt: convertTimestamp(data.updatedAt || new Date()),
          };
          setGalleryItem(item);
        } else {
          toast({
            title: 'Error',
            description: 'Gallery item not found',
            variant: 'destructive',
          });
          router.push('/gallery');
        }
      } catch (error) {
        console.error('Error fetching gallery item:', error);
        toast({
          title: 'Error',
          description: 'Failed to load gallery item',
          variant: 'destructive',
        });
        router.push('/gallery');
      } finally {
        setLoading(false);
      }
    };

    fetchGalleryItem();
  }, [id, router, toast]);

  const handleSubmit = async (_data: Omit<GalleryItem, 'id'>) => {
    try {
      // The actual update will be handled by the parent component
      toast({
        title: 'Success',
        description: 'Gallery item updated successfully',
      });
      router.push('/dashboard/gallery');
    } catch (error) {
      console.error('Error updating gallery item:', error);
      toast({
        title: 'Error',
        description: 'Failed to update gallery item',
        variant: 'destructive',
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-royalBlue-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Edit Gallery Item</h1>
        <p className="text-sm text-muted-foreground">
          Update the gallery item details
        </p>
      </div>
      <div className="max-w-3xl">
        <GalleryForm
          initialData={galleryItem}
          onSubmit={handleSubmit}
          loading={loading}
        />
      </div>
    </div>
  );
}
