'use client';

import { format } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { ExternalLink, Loader2 } from 'lucide-react';
import { Partner } from '@/types/partner';
import Image from 'next/image';
import { Switch } from '@/components/ui/switch';
import { ActionButtons } from '@/components/shared/actions/action-buttons';

interface PartnersTableProps {
  data: Partner[];
  onDelete: (id: string) => void;
  onUpdate: (id: string, updates: Partial<Partner>) => Promise<void>;
  loading?: boolean;
  updatingId?: string | null;
  deletingId?: string | null;
}

export function PartnersTable({ 
  data, 
  onDelete, 
  onUpdate,
  loading = false,
  updatingId = null,
  deletingId = null 
}: PartnersTableProps) {
  
  const handleStatusChange = async (id: string, newStatus: 'draft' | 'published') => {
    try {
      await onUpdate(id, { status: newStatus });
    } catch (error) {
      console.error('Error updating partner status:', error);
    }
  };

  const handleFeaturedChange = async (id: string, isFeatured: boolean) => {
    try {
      await onUpdate(id, { featured: isFeatured });
    } catch (error) {
      console.error('Error updating partner featured status:', error);
    }
  };

  const handleDelete = async (id?: string): Promise<boolean> => {
    if (!id) {
      console.error('No ID provided for deletion');
      return false;
    }
    try {
      onDelete(id);
      return true;
    } catch (error) {
      console.error('Error deleting partner:', error);
      return false;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <Loader2 className="w-8 h-8 animate-spin text-royal-blue-600" />
      </div>
    );
  }

  return (
    <div className="rounded-lg border border-gray-200 shadow-lg bg-white overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Logo</TableHead>
            <TableHead>Name</TableHead>
            <TableHead className="hidden md:table-cell">Type</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Featured</TableHead>
            <TableHead className="hidden md:table-cell">Created</TableHead>
            <TableHead className="w-[100px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.length > 0 ? (
            data.map((partner) => (
              <TableRow key={partner.id}>
                <TableCell>
                  <div className="flex items-center">
                    {partner.logoUrl ? (
                      <div className="relative h-12 w-12 rounded-md overflow-hidden border bg-white flex items-center justify-center">
                        <Image
                          src={partner.logoUrl}
                          alt={partner.name}
                          width={48}
                          height={48}
                          className="object-contain p-1"
                          onError={(e) => {
                            // Fallback to a placeholder if image fails to load
                            const target = e.target as HTMLImageElement;
                            target.onerror = null;
                            target.src = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0OCIgaGVpZ2h0PSI0OCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiYjeDIxMjEyMTsiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cmVjdCB4PSIzIiB5PSIzIiB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHJ4PSIyIiByeT0iMiIvPjxjaXJjbGUgY3g9IjguNSIgY3k9IjguNSIgcj0iMS41Ii8+PHBhdGggZD0iTTIxIDE1bC01LjUyMi01LjE4QTMgMyAwIDAgMCAxMiAxMCIvPjwvc3ZnPg==';
                          }}
                        />
                      </div>
                    ) : (
                      <div className="h-12 w-12 rounded-md bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                        <span className="text-xs text-gray-600 dark:text-gray-300">No logo</span>
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell className="font-medium">
                  <div className="space-y-1">
                    <div className="font-semibold text-gray-900 dark:text-gray-100">{partner.name}</div>
                    {partner.websiteUrl && (
                      <a
                        href={partner.websiteUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-royal-blue-600 hover:underline flex items-center"
                        onClick={(e) => e.stopPropagation()}
                      >
                        {partner.websiteUrl.replace(/^https?:\/\//, '')}
                        <ExternalLink className="w-3 h-3 ml-1" />
                      </a>
                    )}
                  </div>
                </TableCell>
                <TableCell className="hidden md:table-cell">
                  <Badge variant="outline" className="capitalize">
                    {partner.type?.toLowerCase() || 'partner'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant={partner.status === 'published' ? 'success' : 'outline'}
                    >
                      {partner.status === 'published' ? 'Published' : 'Draft'}
                    </Badge>
                    <Switch
                      checked={partner.status === 'published'}
                      onCheckedChange={(checked) =>
                        handleStatusChange(partner.id, checked ? 'published' : 'draft')
                      }
                      disabled={updatingId === partner.id}
                      className={updatingId === partner.id ? 'opacity-50' : ''}
                    />
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <Switch
                      checked={partner.featured}
                      onCheckedChange={(checked) => 
                        handleFeaturedChange(partner.id, checked)
                      }
                      disabled={updatingId === partner.id}
                      className={updatingId === partner.id ? 'opacity-50' : ''}
                    />
                    <span className="ml-2 text-sm text-muted-foreground hidden md:inline">
                      {partner.featured ? 'Featured' : 'Regular'}
                    </span>
                  </div>
                </TableCell>
                <TableCell className="hidden md:table-cell">
                  {partner.createdAt ? (
                    <div className="text-sm text-muted-foreground">
                      {format(new Date(partner.createdAt), 'MMM d, yyyy')}
                    </div>
                  ) : (
                    <span className="text-muted-foreground">-</span>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end items-center space-x-1">
                    <ActionButtons
                      editHref={`/dashboard/partners/${partner.id}/edit`}
                      onDelete={handleDelete}
                      deleteId={partner.id}
                      deleteTitle={`Delete ${partner.name}`}
                      deleteDescription={`Are you sure you want to delete ${partner.name}? This action cannot be undone.`}
                      editButtonProps={{
                        variant: 'ghost',
                        size: 'icon',
                        className: 'h-8 w-8 text-royal-blue-600 hover:text-royal-blue-800 hover:bg-royal-blue-50',
                        disabled: updatingId === partner.id || deletingId === partner.id
                      }}
                      deleteButtonProps={{
                        variant: 'ghost',
                        size: 'icon',
                        className: 'h-8 w-8 text-red-600 hover:text-red-800 hover:bg-red-50',
                        disabled: updatingId === partner.id || deletingId === partner.id
                      }}
                    />
                    {(updatingId === partner.id || deletingId === partner.id) && (
                      <Loader2 className="w-4 h-4 ml-1 animate-spin text-gray-500 dark:text-gray-400" />
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={7} className="h-24 text-center">
                No partners found.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
