'use client';

import { Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DeleteConfirm } from './delete-confirm';
import { EditButton } from './edit-button';

type EditButtonProps = {
  href: string;
  label?: string;
  className?: string;
  variant?: 'ghost' | 'outline' | 'link' | 'default' | 'destructive' | 'secondary';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  disabled?: boolean;
};

type ActionButtonsProps = {
  editHref: string;
  onDelete: (id?: string) => Promise<boolean>;
  deleteTitle: string;
  deleteDescription: string;
  onSuccess?: () => void;
  editButtonProps?: Partial<Omit<EditButtonProps, 'href'>>;
  deleteButtonProps?: Partial<{
    variant?: 'ghost' | 'outline' | 'link' | 'default' | 'destructive' | 'secondary';
    size?: 'default' | 'sm' | 'lg' | 'icon';
    disabled?: boolean;
    className?: string;
  }>;
  className?: string;
  deleteId?: string;
};

export function ActionButtons({
  editHref,
  onDelete,
  deleteTitle,
  deleteDescription,
  onSuccess,
  editButtonProps = {},
  deleteButtonProps = {},
  className = '',
  deleteId = '',
}: ActionButtonsProps) {
  const handleDelete = async (id: string) => {
    try {
      const success = await onDelete(id);
      if (success && onSuccess) {
        onSuccess();
      }
      return success;
    } catch (error) {
      console.error('Error in delete handler:', error);
      return false;
    }
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <EditButton href={editHref} {...editButtonProps} />
      <DeleteConfirm
        id={deleteId || ''}
        title={deleteTitle}
        description={deleteDescription}
        onConfirm={handleDelete}
        onSuccess={onSuccess}
        trigger={
          <Button
            variant="destructive"
            size="sm"
            className="h-8"
            {...deleteButtonProps}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        }
      />
    </div>
  );
}

export default ActionButtons;
