'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { useSidebar } from '@/hooks/use-sidebar'
import { Button } from '@/components/ui/button'
import {
  Home,
  Newspaper,
  Image as ImageIcon,
  Calendar,
  Users,
  Settings,
  ChevronLeft,
  ChevronRight,
  Handshake,
  MailCheck
} from 'lucide-react'
import { useState, useEffect } from 'react'

type NavItem = {
  name: string
  href: string
  icon: React.ReactNode
  activeIcon?: React.ReactNode
}

const navigation: NavItem[] = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: <Home className="h-5 w-5" />,
    activeIcon: <Home className="h-5 w-5 text-primary" />
  },
  {
    name: 'News',
    href: '/dashboard/news',
    icon: <Newspaper className="h-5 w-5" />,
    activeIcon: <Newspaper className="h-5 w-5 text-primary" />
  },
  {
    name: 'Events',
    href: '/dashboard/events',
    icon: <Calendar className="h-5 w-5" />,
    activeIcon: <Calendar className="h-5 w-5 text-primary" />
  },
  {
    name: 'Gallery',
    href: '/dashboard/gallery',
    icon: <ImageIcon className="h-5 w-5" />,
    activeIcon: <ImageIcon className="h-5 w-5 text-primary" />
  },
  {
    name: 'Partners',
    href: '/dashboard/partners',
    icon: <Handshake className="h-5 w-5" />,
    activeIcon: <Handshake className="h-5 w-5 text-primary" />
  },
  {
    name: 'Users',
    href: '/dashboard/users',
    icon: <Users className="h-5 w-5" />,
    activeIcon: <Users className="h-5 w-5 text-primary" />
  },
  {
    name: 'Settings',
    href: '/dashboard/settings',
    icon: <Settings className="h-5 w-5" />,
    activeIcon: <Settings className="h-5 w-5 text-primary" />
  },
  {
    name: 'RSVPs',
    href: '/dashboard/rsvp',
    icon: <MailCheck className="h-5 w-5" />,
    activeIcon: <MailCheck className="h-5 w-5 text-primary" />
  }
]

import { useAdminAuth } from '@/contexts/AdminAuthContext'

export function Sidebar() {
  const pathname = usePathname()
  const { isOpen, setOpen, isCollapsed, toggleCollapsed } = useSidebar()
  const [isMobile, setIsMobile] = useState(false)
  const { user } = useAdminAuth()

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkIfMobile()
    window.addEventListener('resize', checkIfMobile)

    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])

  // Toggle sidebar collapse state (only on desktop)
  const handleToggleCollapse = () => {
    if (!isMobile) {
      toggleCollapsed();
    }
  };



  return (
    <>
      {(!isMobile || isOpen) && (
        <div className={cn(
          "flex h-full flex-col bg-white transition-transform duration-300",
          isMobile ? (isOpen ? 'w-64 min-w-[16rem] translate-x-0' : '-translate-x-full w-64 min-w-[16rem]') : 'w-full',
          isMobile && 'fixed left-0 top-0 z-50 h-screen',
        )}>
    

        {/* Sidebar header */}
        <div className={cn(
          "flex items-center justify-between h-16 border-b border-royal-blue-700 bg-gradient-to-r from-royal-blue-600 via-royal-blue-700 to-royal-blue-800 relative shadow-lg text-white",
          isCollapsed ? 'px-3' : 'px-4'
        )}>
          {/* Pattern overlay */}
          <div className="absolute inset-0 opacity-10 royal-pattern pointer-events-none" />
          {!isCollapsed && (
            <h1 className="text-2xl font-extrabold whitespace-nowrap text-white drop-shadow-md flex items-center">
              <span className="text-royal-gold-400">Adukrom</span> Admin
            </h1>
          )}
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={() => {
              if (isMobile) {
                setOpen(false);
              } else {
                handleToggleCollapse();
              }
            }}
            aria-label={isCollapsed ? (isMobile ? 'Close sidebar' : 'Expand sidebar') : (isMobile ? 'Close sidebar' : 'Collapse sidebar')}
          >
            {isCollapsed ? (
              <ChevronRight className="h-5 w-5" />
            ) : (
              <ChevronLeft className="h-5 w-5" />
            )}
          </Button>
        </div>
        
        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto py-4 px-2 bg-gradient-to-b from-royal-blue-700 via-royal-blue-800 to-royal-blue-900 relative text-white">
          <div className="space-y-1 relative">
            {navigation.map((item) => {
              const isActive = pathname ? 
                (pathname.startsWith(item.href) || 
                // Handle root dashboard path
                (pathname === '/dashboard' && item.href === '/dashboard') ||
                // Handle index routes
                (pathname === `${item.href}/` || `${pathname}/` === item.href)) : false;
              
              const Icon = isActive ? (item.activeIcon || item.icon) : item.icon
              
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                    isActive
                      ? 'bg-royal-gold-500/20 text-royal-gold-300 font-semibold border-r-2 border-royal-gold-400'
                      : 'text-gray-300 hover:bg-white/10 hover:text-white',
                    isCollapsed ? 'justify-center' : 'px-4'
                  )}
                  onClick={() => {
                    // Close mobile sidebar when an item is clicked
                    if (isMobile) {
                      setOpen(false);
                    }
                  }}
                >
                  <span className={cn(
                    'flex items-center justify-center',
                    isCollapsed ? 'w-6' : isMobile ? 'w-8 mr-4' : 'w-6 mr-3',
                    isMobile && 'text-xl'
                  )}>
                    {Icon}
                  </span>
                  {(!isCollapsed || isMobile) && (
                    <span className={cn(
                      isMobile ? 'text-base font-semibold' : ''
                    )}>{item.name}</span>
                  )}
                </Link>
              )
            })}
          </div>
        </nav>
        
        {/* User profile */}
        <div className={cn(
          'border-t border-royal-blue-700 bg-royal-blue-800 p-4',
          isCollapsed ? 'px-2' : 'px-4'
        )}>
          <div className={cn(
            'flex items-center',
            isCollapsed ? 'justify-center' : 'justify-between'
          )}>
            {!isCollapsed && (
              <div className="flex items-center min-w-0">
                <div className="h-9 w-9 rounded-full bg-royal-gold-500 flex items-center justify-center flex-shrink-0">
                  <span className="text-black font-bold">AD</span>
                </div>
                <div className="ml-3 min-w-0">
                  <p className="text-sm font-medium text-white truncate">
                    Admin User
                  </p>
                  <p className="text-xs text-royal-gold-300 truncate">
                    {user?.email || '<EMAIL>'}
                  </p>
                </div>
              </div>
            )}
            {isCollapsed && (
              <div className="h-9 w-9 rounded-full bg-royal-gold-500 flex items-center justify-center mx-auto">
                <span className="text-black font-bold text-sm">AD</span>
              </div>
            )}
          </div>
        </div>
        </div>
      )}
    </>
  )
}
