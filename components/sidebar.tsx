'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { useSidebar } from '@/hooks/use-sidebar'
import { Button } from '@/components/ui/button'
import {
  Home,
  Newspaper,
  Image as ImageIcon,
  Calendar,
  Users,
  Settings,
  ChevronLeft,
  ChevronRight,
  Handshake,
  MailCheck
} from 'lucide-react'
import { useState, useEffect } from 'react'

type NavItem = {
  name: string
  href: string
  icon: React.ReactNode
  activeIcon?: React.ReactNode
}

const navigation: NavItem[] = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: <Home className="h-5 w-5" />,
    activeIcon: <Home className="h-5 w-5 text-primary" />
  },
  {
    name: 'News',
    href: '/dashboard/news',
    icon: <Newspaper className="h-5 w-5" />,
    activeIcon: <Newspaper className="h-5 w-5 text-primary" />
  },
  {
    name: 'Events',
    href: '/dashboard/events',
    icon: <Calendar className="h-5 w-5" />,
    activeIcon: <Calendar className="h-5 w-5 text-primary" />
  },
  {
    name: 'Gallery',
    href: '/dashboard/gallery',
    icon: <ImageIcon className="h-5 w-5" />,
    activeIcon: <ImageIcon className="h-5 w-5 text-primary" />
  },
  {
    name: 'Partners',
    href: '/dashboard/partners',
    icon: <Handshake className="h-5 w-5" />,
    activeIcon: <Handshake className="h-5 w-5 text-primary" />
  },
  {
    name: 'Users',
    href: '/dashboard/users',
    icon: <Users className="h-5 w-5" />,
    activeIcon: <Users className="h-5 w-5 text-primary" />
  },
  {
    name: 'Settings',
    href: '/dashboard/settings',
    icon: <Settings className="h-5 w-5" />,
    activeIcon: <Settings className="h-5 w-5 text-primary" />
  },
  {
    name: 'RSVPs',
    href: '/dashboard/rsvp',
    icon: <MailCheck className="h-5 w-5" />,
    activeIcon: <MailCheck className="h-5 w-5 text-primary" />
  }
]

import { useAdminAuth } from '@/contexts/AdminAuthContext'

export function Sidebar() {
  const pathname = usePathname()
  const { isOpen, setOpen, isCollapsed, toggleCollapsed } = useSidebar()
  const [isMobile, setIsMobile] = useState(false)
  const { user } = useAdminAuth()

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkIfMobile()
    window.addEventListener('resize', checkIfMobile)

    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])

  // Toggle sidebar collapse state (only on desktop)
  const handleToggleCollapse = () => {
    if (!isMobile) {
      toggleCollapsed();
    }
  };



  return (
    <>
      {(!isMobile || isOpen) && (
        <div
          className={cn(
            "flex h-full flex-col transition-transform duration-300 shadow-2xl",
            isMobile ? (isOpen ? 'w-64 min-w-[16rem] translate-x-0' : '-translate-x-full w-64 min-w-[16rem]') : 'w-full',
            isMobile && 'fixed left-0 top-0 z-50 h-screen',
          )}
          style={{
            background: 'linear-gradient(180deg, #1e3a8a 0%, #1d4ed8 50%, #142a5e 100%)'
          }}
        >
    

        {/* Sidebar header */}
        <div
          className={cn(
            "flex items-center justify-between h-16 relative shadow-xl text-white",
            isCollapsed ? 'px-3' : 'px-4'
          )}
          style={{
            background: 'linear-gradient(90deg, #1e40af 0%, #142a5e 50%, #1e40af 100%)',
            borderBottom: '1px solid rgba(212, 175, 55, 0.3)'
          }}
        >
          {/* Gold accent line */}
          <div
            className="absolute bottom-0 left-0 right-0 h-0.5"
            style={{
              background: 'linear-gradient(90deg, transparent 0%, #d4af37 50%, transparent 100%)'
            }}
          />
          {/* Pattern overlay */}
          <div className="absolute inset-0 opacity-10 royal-pattern pointer-events-none" />
          {!isCollapsed && (
            <h1 className="text-2xl font-extrabold whitespace-nowrap text-white drop-shadow-lg flex items-center relative z-10">
              <span style={{ color: '#d4af37' }} className="drop-shadow-md">Adukrom</span>
              <span className="ml-1 text-white">Admin</span>
            </h1>
          )}
          {isCollapsed && (
            <div className="flex items-center justify-center w-full">
              <div
                className="h-10 w-10 rounded-lg flex items-center justify-center shadow-lg"
                style={{
                  background: 'linear-gradient(135deg, #d4af37 0%, #ca8a04 100%)'
                }}
              >
                <span style={{ color: '#1e3a8a' }} className="font-black text-lg">A</span>
              </div>
            </div>
          )}
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 relative z-10 hover:bg-white/10"
            style={{ color: '#d4af37' }}
            onClick={() => {
              if (isMobile) {
                setOpen(false);
              } else {
                handleToggleCollapse();
              }
            }}
            aria-label={isCollapsed ? (isMobile ? 'Close sidebar' : 'Expand sidebar') : (isMobile ? 'Close sidebar' : 'Collapse sidebar')}
          >
            {isCollapsed ? (
              <ChevronRight className="h-5 w-5" />
            ) : (
              <ChevronLeft className="h-5 w-5" />
            )}
          </Button>
        </div>
        
        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto py-6 px-3 relative text-white">
          {/* Subtle gold accent at top */}
          <div
            className="absolute top-0 left-4 right-4 h-px"
            style={{
              background: 'linear-gradient(90deg, transparent 0%, rgba(212, 175, 55, 0.3) 50%, transparent 100%)'
            }}
          />

          <div className="space-y-2 relative">
            {navigation.map((item) => {
              // Handle active state for navigation items
              let isActive = false;
              if (pathname) {
                // Handle exact matches
                if (pathname === item.href) {
                  isActive = true;
                } 
                // Handle nested routes
                else if (item.href !== '/dashboard' && pathname.startsWith(`${item.href}/`)) {
                  isActive = true;
                }
                // Handle dashboard specifically
                else if (item.href === '/dashboard' && pathname === '/dashboard') {
                  isActive = true;
                }
              }

              const Icon = isActive ? (item.activeIcon || item.icon) : item.icon

              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    'group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 relative overflow-hidden',
                    isActive
                      ? 'bg-gradient-to-r from-royal-gold-500/25 to-royal-gold-600/20 text-royal-gold-200 font-semibold shadow-lg border border-royal-gold-400/30'
                      : 'text-gray-300 hover:bg-gradient-to-r hover:from-white/10 hover:to-white/5 hover:text-white hover:shadow-md',
                    isCollapsed ? 'justify-center px-2' : 'px-4'
                  )}
                  onClick={() => {
                    // Close mobile sidebar when an item is clicked
                    if (isMobile) {
                      setOpen(false);
                    }
                  }}
                >
                  {/* Active indicator */}
                  {isActive && (
                    <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-royal-gold-400 to-royal-gold-600 rounded-r-full" />
                  )}

                  <span className={cn(
                    'flex items-center justify-center transition-transform group-hover:scale-110',
                    isCollapsed ? 'w-6' : isMobile ? 'w-8 mr-4' : 'w-6 mr-3',
                    isMobile && 'text-xl',
                    isActive && 'text-royal-gold-300'
                  )}>
                    {Icon}
                  </span>
                  {(!isCollapsed || isMobile) && (
                    <span className={cn(
                      'transition-colors',
                      isMobile ? 'text-base font-semibold' : '',
                      isActive && 'text-royal-gold-200'
                    )}>{item.name}</span>
                  )}

                  {/* Hover glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-royal-gold-400/0 via-royal-gold-400/5 to-royal-gold-400/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg" />
                </Link>
              )
            })}
          </div>
        </nav>
        
        {/* User profile */}
        <div className={cn(
          'border-t border-royal-gold-400/30 bg-gradient-to-r from-royal-blue-900 via-royal-blue-800 to-royal-blue-900 p-4 relative',
          isCollapsed ? 'px-2' : 'px-4'
        )}>
          {/* Gold accent line at top */}
          <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-royal-gold-400 to-transparent" />

          <div className={cn(
            'flex items-center',
            isCollapsed ? 'justify-center' : 'justify-between'
          )}>
            {!isCollapsed && (
              <div className="flex items-center min-w-0">
                <div className="h-10 w-10 rounded-full bg-gradient-to-br from-royal-gold-400 to-royal-gold-600 flex items-center justify-center flex-shrink-0 shadow-lg border-2 border-royal-gold-300/50">
                  <span className="text-royal-blue-900 font-black text-sm">AD</span>
                </div>
                <div className="ml-3 min-w-0">
                  <p className="text-sm font-semibold text-white truncate">
                    Admin User
                  </p>
                  <p className="text-xs text-royal-gold-300 truncate font-medium">
                    {user?.email || '<EMAIL>'}
                  </p>
                </div>
              </div>
            )}
            {isCollapsed && (
              <div className="h-10 w-10 rounded-full bg-gradient-to-br from-royal-gold-400 to-royal-gold-600 flex items-center justify-center mx-auto shadow-lg border-2 border-royal-gold-300/50">
                <span className="text-royal-blue-900 font-black text-sm">AD</span>
              </div>
            )}
          </div>
        </div>
        </div>
      )}
    </>
  )
}
