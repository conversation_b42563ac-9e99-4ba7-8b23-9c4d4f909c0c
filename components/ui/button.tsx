import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-royal-blue-500 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default:
          "bg-royal-blue-600 text-white shadow-lg hover:bg-royal-blue-700 hover:shadow-xl transform hover:-translate-y-0.5",
        destructive:
          "bg-red-600 text-white shadow-lg hover:bg-red-700 hover:shadow-xl transform hover:-translate-y-0.5",
        outline:
          "border-2 border-royal-blue-600 bg-white text-royal-blue-600 shadow-sm hover:bg-royal-blue-50 hover:border-royal-blue-700",
        secondary:
          "bg-royal-gold-500 text-black shadow-lg hover:bg-royal-gold-600 hover:shadow-xl transform hover:-translate-y-0.5",
        ghost: "text-royal-blue-600 hover:bg-royal-blue-50 hover:text-royal-blue-700",
        link: "text-royal-blue-600 underline-offset-4 hover:underline hover:text-royal-blue-700",
        royal: "bg-gradient-to-r from-royal-blue-600 to-royal-blue-700 text-white shadow-lg hover:from-royal-blue-700 hover:to-royal-blue-800 hover:shadow-xl transform hover:-translate-y-0.5",
        gold: "bg-gradient-to-r from-royal-gold-500 to-royal-gold-600 text-black shadow-lg hover:from-royal-gold-600 hover:to-royal-gold-700 hover:shadow-xl transform hover:-translate-y-0.5",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
