import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-3 py-1 text-xs font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-royal-blue-500 focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-royal-blue-600 text-white shadow-lg hover:bg-royal-blue-700 hover:shadow-xl",
        secondary:
          "border-transparent bg-royal-gold-500 text-black shadow-lg hover:bg-royal-gold-600 hover:shadow-xl",
        destructive:
          "border-transparent bg-red-600 text-white shadow-lg hover:bg-red-700 hover:shadow-xl",
        outline: "border-royal-blue-600 text-royal-blue-600 bg-white hover:bg-royal-blue-50",
        success: "border-transparent bg-green-600 text-white shadow-lg hover:bg-green-700 hover:shadow-xl",
        warning: "border-transparent bg-yellow-500 text-black shadow-lg hover:bg-yellow-600 hover:shadow-xl",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
