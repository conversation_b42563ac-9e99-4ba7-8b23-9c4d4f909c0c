'use client';

import { useState, useEffect } from 'react';
import { auth, db, storage } from '@/lib/firebase';
import { collection, getDocs, connectFirestoreEmulator } from 'firebase/firestore';
import { connectAuthEmulator } from 'firebase/auth';
import { connectStorageEmulator } from 'firebase/storage';
import { CheckCircle, XCircle, AlertCircle, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface ConnectionStatus {
  auth: 'checking' | 'connected' | 'error';
  firestore: 'checking' | 'connected' | 'error';
  storage: 'checking' | 'connected' | 'error';
  authError?: string;
  firestoreError?: string;
  storageError?: string;
}

export function FirebaseConnectionTest() {
  const [status, setStatus] = useState<ConnectionStatus>({
    auth: 'checking',
    firestore: 'checking',
    storage: 'checking',
  });
  const [isVisible, setIsVisible] = useState(false);

  const testConnections = async () => {
    setStatus({
      auth: 'checking',
      firestore: 'checking',
      storage: 'checking',
    });

    // Test Auth
    try {
      console.log('[Firebase Test] Testing Auth connection...');
      console.log('[Firebase Test] Auth instance:', auth);
      console.log('[Firebase Test] Auth app:', auth.app);
      console.log('[Firebase Test] Auth config:', auth.config);
      
      setStatus(prev => ({ ...prev, auth: 'connected' }));
    } catch (error) {
      console.error('[Firebase Test] Auth error:', error);
      setStatus(prev => ({ 
        ...prev, 
        auth: 'error', 
        authError: error instanceof Error ? error.message : 'Unknown error' 
      }));
    }

    // Test Firestore
    try {
      console.log('[Firebase Test] Testing Firestore connection...');
      console.log('[Firebase Test] Firestore instance:', db);
      console.log('[Firebase Test] Firestore app:', db.app);
      
      // Try to read from a collection
      const testCollection = collection(db, 'test');
      await getDocs(testCollection);
      
      setStatus(prev => ({ ...prev, firestore: 'connected' }));
    } catch (error) {
      console.error('[Firebase Test] Firestore error:', error);
      setStatus(prev => ({ 
        ...prev, 
        firestore: 'error', 
        firestoreError: error instanceof Error ? error.message : 'Unknown error' 
      }));
    }

    // Test Storage
    try {
      console.log('[Firebase Test] Testing Storage connection...');
      console.log('[Firebase Test] Storage instance:', storage);
      console.log('[Firebase Test] Storage app:', storage.app);
      
      setStatus(prev => ({ ...prev, storage: 'connected' }));
    } catch (error) {
      console.error('[Firebase Test] Storage error:', error);
      setStatus(prev => ({ 
        ...prev, 
        storage: 'error', 
        storageError: error instanceof Error ? error.message : 'Unknown error' 
      }));
    }
  };

  useEffect(() => {
    if (isVisible) {
      testConnections();
    }
  }, [isVisible]);

  const getStatusIcon = (serviceStatus: 'checking' | 'connected' | 'error') => {
    switch (serviceStatus) {
      case 'checking':
        return <Loader2 className="h-5 w-5 animate-spin text-blue-500" />;
      case 'connected':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
    }
  };

  const getStatusText = (serviceStatus: 'checking' | 'connected' | 'error') => {
    switch (serviceStatus) {
      case 'checking':
        return 'Checking...';
      case 'connected':
        return 'Connected';
      case 'error':
        return 'Error';
    }
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          variant="outline"
          size="sm"
          className="bg-white shadow-lg"
        >
          <AlertCircle className="h-4 w-4 mr-2" />
          Test Firebase
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Card className="w-80 shadow-lg">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm">Firebase Connection Test</CardTitle>
            <Button
              onClick={() => setIsVisible(false)}
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm">Authentication</span>
            <div className="flex items-center space-x-2">
              {getStatusIcon(status.auth)}
              <span className="text-sm">{getStatusText(status.auth)}</span>
            </div>
          </div>
          {status.authError && (
            <p className="text-xs text-red-600 ml-4">{status.authError}</p>
          )}

          <div className="flex items-center justify-between">
            <span className="text-sm">Firestore</span>
            <div className="flex items-center space-x-2">
              {getStatusIcon(status.firestore)}
              <span className="text-sm">{getStatusText(status.firestore)}</span>
            </div>
          </div>
          {status.firestoreError && (
            <p className="text-xs text-red-600 ml-4">{status.firestoreError}</p>
          )}

          <div className="flex items-center justify-between">
            <span className="text-sm">Storage</span>
            <div className="flex items-center space-x-2">
              {getStatusIcon(status.storage)}
              <span className="text-sm">{getStatusText(status.storage)}</span>
            </div>
          </div>
          {status.storageError && (
            <p className="text-xs text-red-600 ml-4">{status.storageError}</p>
          )}

          <div className="pt-2 border-t">
            <Button
              onClick={testConnections}
              variant="outline"
              size="sm"
              className="w-full"
            >
              Retest Connections
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
