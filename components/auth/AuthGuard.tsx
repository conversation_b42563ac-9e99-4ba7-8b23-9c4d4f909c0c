'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import { Loader2 } from 'lucide-react';

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  allowedRoles?: string[];
  fallbackPath?: string;
}

export function AuthGuard({ 
  children, 
  requireAuth = true, 
  allowedRoles = ['super_admin', 'admin', 'editor'],
  fallbackPath = '/login'
}: AuthGuardProps) {
  const { user, loading, isAdmin } = useAdminAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    if (!loading) {
      if (requireAuth) {
        if (!user) {
          // No user, redirect to login
          router.push(fallbackPath);
          return;
        }

        if (!user.role || !allowedRoles.includes(user.role)) {
          // User doesn't have required role
          console.error('[AuthGuard] Access denied. Required roles:', allowedRoles, 'User role:', user.role);
          router.push('/login');
          return;
        }

        // User is authenticated and has required role
        setIsChecking(false);
      } else {
        // No auth required
        setIsChecking(false);
      }
    }
  }, [user, loading, isAdmin, requireAuth, allowedRoles, router, fallbackPath]);

  // Show loading while checking authentication
  if (loading || isChecking) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-sm text-gray-600">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // If we reach here, user is authenticated or auth is not required
  return <>{children}</>;
}

// Specific guards for different access levels
export function SuperAdminGuard({ children }: { children: React.ReactNode }) {
  return (
    <AuthGuard allowedRoles={['super_admin']}>
      {children}
    </AuthGuard>
  );
}

export function AdminGuard({ children }: { children: React.ReactNode }) {
  return (
    <AuthGuard allowedRoles={['super_admin', 'admin']}>
      {children}
    </AuthGuard>
  );
}

export function EditorGuard({ children }: { children: React.ReactNode }) {
  return (
    <AuthGuard allowedRoles={['super_admin', 'admin', 'editor']}>
      {children}
    </AuthGuard>
  );
}
